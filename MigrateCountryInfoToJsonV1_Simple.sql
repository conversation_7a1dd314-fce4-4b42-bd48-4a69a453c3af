-- SIMPLE MIGRATION QUERY - EXECUTE THIS TO MIGRATE ALL EXISTING DATA
-- This query migrates all CountryInfo and Currencies data to JSON format in GlobalSettings.CountriesV1

-- Step 1: Migrate all existing CountryInfo data to JSON format
UPDATE "LeadratBlack"."GlobalSettings" 
SET "CountriesV1" = (
    SELECT COALESCE(
        json_agg(
            json_build_object(
                'Name', ci."Name",
                'CallingCode', ci."CallingCode",
                'DefaultCallingCode', ci."DefaultCallingCode",
                'Codes', ci."Codes",
                'Code', ci."Code",
                'DefaultCurrency', ci."DefaultCurrency",
                'Icon', ci."Icon",
                'TimeZoneId', ci."TimeZoneId",
                'DefaultSymbol', ci."DefaultSymbol",
                'Currencies', (
                    SELECT COALESCE(
                        json_agg(
                            json_build_object(
                                'Currency', c."Currency",
                                'Symbol', c."Symbol"
                            )
                        ), 
                        '[]'::json
                    )
                    FROM "LeadratBlack"."Currencies" c 
                    WHERE c."CountryInfoId" = ci."Id" 
                    AND c."DeletedOn" IS NULL
                )
            )
        )::text, 
        '[]'
    )
    FROM "LeadratBlack"."CountryInfo" ci 
    WHERE ci."GlobalSettingsId" = "GlobalSettings"."Id" 
    AND ci."DeletedOn" IS NULL
);

-- Step 2: Set empty JSON array for GlobalSettings without CountryInfo records
UPDATE "LeadratBlack"."GlobalSettings" 
SET "CountriesV1" = '[]'
WHERE ("CountriesV1" IS NULL OR "CountriesV1" = '' OR "CountriesV1" = '{}')
AND "Id" NOT IN (
    SELECT DISTINCT gs."Id" 
    FROM "LeadratBlack"."GlobalSettings" gs
    INNER JOIN "LeadratBlack"."CountryInfo" ci ON ci."GlobalSettingsId" = gs."Id"
    WHERE ci."DeletedOn" IS NULL
);

-- Verification: Check migration results
SELECT 
    COUNT(*) as total_global_settings,
    COUNT(CASE WHEN "CountriesV1" IS NOT NULL AND "CountriesV1" != '' THEN 1 END) as migrated_records,
    COUNT(CASE WHEN "CountriesV1" = '[]' THEN 1 END) as empty_countries
FROM "LeadratBlack"."GlobalSettings";
