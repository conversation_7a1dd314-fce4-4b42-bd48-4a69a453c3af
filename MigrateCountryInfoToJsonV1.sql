-- COMPLETE DATABASE MIGRATION QUERY
-- Migrate all existing CountryInfo data to JSON format for CountriesV1 field
-- Execute this query to migrate ALL existing data immediately

-- MAIN MIGRATION QUERY - Execute this to migrate all data
UPDATE "LeadratBlack"."GlobalSettings"
SET "CountriesV1" = (
    SELECT COALESCE(
        json_agg(
            json_build_object(
                'Name', ci."Name",
                'CallingCode', ci."CallingCode",
                'DefaultCallingCode', ci."DefaultCallingCode",
                'Codes', ci."Codes",
                'Code', ci."Code",
                'DefaultCurrency', ci."DefaultCurrency",
                'Icon', ci."Icon",
                'TimeZoneId', ci."TimeZoneId",
                'DefaultSymbol', ci."DefaultSymbol",
                'Currencies', (
                    SELECT COALESCE(
                        json_agg(
                            json_build_object(
                                'Currency', c."Currency",
                                'Symbol', c."Symbol"
                            )
                        ),
                        '[]'::json
                    )
                    FROM "LeadratBlack"."Currencies" c
                    WHERE c."CountryInfoId" = ci."Id"
                    AND c."DeletedOn" IS NULL
                )
            )
        )::text,
        '[]'
    )
    FROM "LeadratBlack"."CountryInfo" ci
    WHERE ci."GlobalSettingsId" = "GlobalSettings"."Id"
    AND ci."DeletedOn" IS NULL
);

-- Set empty JSON array for GlobalSettings without CountryInfo records
UPDATE "LeadratBlack"."GlobalSettings"
SET "CountriesV1" = '[]'
WHERE ("CountriesV1" IS NULL OR "CountriesV1" = '' OR "CountriesV1" = '{}')
AND "Id" NOT IN (
    SELECT DISTINCT gs."Id"
    FROM "LeadratBlack"."GlobalSettings" gs
    INNER JOIN "LeadratBlack"."CountryInfo" ci ON ci."GlobalSettingsId" = gs."Id"
    WHERE ci."DeletedOn" IS NULL
);

-- Step 3: Verification query to check the migration results
-- Uncomment the following queries to verify the migration

/*
-- Check how many GlobalSettings records have been updated
SELECT 
    COUNT(*) as total_global_settings,
    COUNT(CASE WHEN "CountriesV1" IS NOT NULL AND "CountriesV1" != '' THEN 1 END) as with_countries_v1,
    COUNT(CASE WHEN "CountriesV1" = '[]' THEN 1 END) as with_empty_countries_v1
FROM "LeadratBlack"."GlobalSettings";

-- Sample the migrated data (first 5 records)
SELECT 
    "Id",
    "CountriesV1"
FROM "LeadratBlack"."GlobalSettings" 
WHERE "CountriesV1" IS NOT NULL AND "CountriesV1" != '[]'
LIMIT 5;

-- Check if any CountryInfo records were not migrated
SELECT 
    ci."Id",
    ci."Name",
    ci."GlobalSettingsId",
    gs."CountriesV1"
FROM "LeadratBlack"."CountryInfo" ci
LEFT JOIN "LeadratBlack"."GlobalSettings" gs ON gs."Id" = ci."GlobalSettingsId"
WHERE ci."DeletedOn" IS NULL
AND (gs."CountriesV1" IS NULL OR gs."CountriesV1" = '' OR gs."CountriesV1" = '{}');
*/

-- Step 4: Optional - Create a backup table before running the migration (recommended)
-- Uncomment the following to create a backup

/*
-- Create backup table for CountryInfo
CREATE TABLE "LeadratBlack"."CountryInfo_Backup_" || to_char(now(), 'YYYYMMDD_HH24MISS') AS 
SELECT * FROM "LeadratBlack"."CountryInfo";

-- Create backup table for Currencies
CREATE TABLE "LeadratBlack"."Currencies_Backup_" || to_char(now(), 'YYYYMMDD_HH24MISS') AS 
SELECT * FROM "LeadratBlack"."Currencies";

-- Create backup table for GlobalSettings (only relevant columns)
CREATE TABLE "LeadratBlack"."GlobalSettings_CountriesV1_Backup_" || to_char(now(), 'YYYYMMDD_HH24MISS') AS 
SELECT "Id", "CountriesV1", "CreatedOn", "LastModifiedOn" FROM "LeadratBlack"."GlobalSettings";
*/

-- Step 5: Performance considerations for large datasets
-- If you have a large number of records, consider running the migration in batches:

/*
-- Example batch processing (process 1000 GlobalSettings records at a time)
DO $$
DECLARE
    batch_size INTEGER := 1000;
    offset_val INTEGER := 0;
    rows_affected INTEGER;
BEGIN
    LOOP
        UPDATE "LeadratBlack"."GlobalSettings" 
        SET "CountriesV1" = (
            SELECT COALESCE(
                json_agg(
                    json_build_object(
                        'Name', ci."Name",
                        'CallingCode', ci."CallingCode",
                        'DefaultCallingCode', ci."DefaultCallingCode",
                        'Codes', ci."Codes",
                        'Code', ci."Code",
                        'DefaultCurrency', ci."DefaultCurrency",
                        'Icon', ci."Icon",
                        'TimeZoneId', ci."TimeZoneId",
                        'DefaultSymbol', ci."DefaultSymbol",
                        'Currencies', (
                            SELECT COALESCE(
                                json_agg(
                                    json_build_object(
                                        'Currency', c."Currency",
                                        'Symbol', c."Symbol"
                                    )
                                ), 
                                '[]'::json
                            )
                            FROM "LeadratBlack"."Currencies" c 
                            WHERE c."CountryInfoId" = ci."Id" 
                            AND c."DeletedOn" IS NULL
                        )
                    )
                )::text, 
                '[]'
            )
            FROM "LeadratBlack"."CountryInfo" ci 
            WHERE ci."GlobalSettingsId" = "GlobalSettings"."Id" 
            AND ci."DeletedOn" IS NULL
        )
        WHERE "Id" IN (
            SELECT "Id" 
            FROM "LeadratBlack"."GlobalSettings" 
            WHERE ("CountriesV1" IS NULL OR "CountriesV1" = '' OR "CountriesV1" = '{}')
            ORDER BY "Id"
            LIMIT batch_size OFFSET offset_val
        );
        
        GET DIAGNOSTICS rows_affected = ROW_COUNT;
        offset_val := offset_val + batch_size;
        
        RAISE NOTICE 'Processed batch starting at offset %, rows affected: %', offset_val - batch_size, rows_affected;
        
        EXIT WHEN rows_affected = 0;
    END LOOP;
END $$;
*/

-- Notes:
-- 1. This migration preserves all existing CountryInfo and Currencies data
-- 2. The JSON structure matches the CountryInfoDto structure used in the application
-- 3. Only non-deleted records are migrated (DeletedOn IS NULL)
-- 4. The migration is idempotent - it only updates records where CountriesV1 is null/empty
-- 5. Consider running the backup queries before executing the migration
-- 6. Test the migration on a development environment first
-- 7. The migration handles the nested Currencies relationship properly
